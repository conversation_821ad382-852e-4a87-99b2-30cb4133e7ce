/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/

import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
import NetInfo from '@react-native-community/netinfo';
import type { AppDispatch } from '@/src/redux/store';
import { showToast } from '@/src/utilities/toast';
import {
  setForumQuestionsFilters,
  clearForumQuestions,
  fetchForumQuestions,
} from '@/src/redux/slices/question/questionSlice';
import { selectForumQuestionsFilters } from '@/src/redux/selectors/question';
import { selectSelectionByKey } from '@/src/redux/selectors/search';
import { clearSelection } from '@/src/redux/slices/entitysearch/searchSlice';
import type { ForumQuestionsFiltersI } from '@/src/redux/slices/question/types';
import type { SearchResultI } from '@/src/redux/slices/entitysearch/types';

export type UseForumFilterResult = {
  filters: ForumQuestionsFiltersI;
  departmentSelection: SearchResultI | null;
  qnaTopicsSelection: SearchResultI | null;
  equipmentSelection: SearchResultI | null;
  makeSelection: SearchResultI | null;
  modelSelection: SearchResultI | null;
  updateFilter: (key: keyof ForumQuestionsFiltersI, value: any) => void;
  addTopic: (topic: SearchResultI) => void;
  removeTopic: (topicId: string) => void;
  setEquipmentCategory: (equipment: SearchResultI | null) => void;
  setEquipmentManufacturer: (manufacturer: SearchResultI | null) => void;
  setEquipmentModel: (model: SearchResultI | null) => void;
  applyFilters: () => void;
  clearAllFilters: () => void;
};

const useForumFilter = (): UseForumFilterResult => {
  const [isConnected, setIsConnected] = useState<boolean | null>(true);
  const dispatch = useDispatch<AppDispatch>();
  const navigation = useNavigation();

  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener((state) => {
      setIsConnected(state.isConnected);
    });

    return () => {
      unsubscribe();
    };
  }, []);
  
  const filters = useSelector(selectForumQuestionsFilters);
  const departmentSelection = useSelector(selectSelectionByKey('department'));
  const qnaTopicsSelection = useSelector(selectSelectionByKey('qnaTopics'));
  const equipmentSelection = useSelector(selectSelectionByKey('equipment'));
  const makeSelection = useSelector(selectSelectionByKey('make'));
  const modelSelection = useSelector(selectSelectionByKey('model'));

  const updateFilter = (key: keyof ForumQuestionsFiltersI, value: any) => {
    dispatch(setForumQuestionsFilters({ [key]: value }));
  };

  const addTopic = (topic: SearchResultI) => {
    const existingTopics = filters.topics || [];
    if (existingTopics.length < 3 && !existingTopics.find(t => t.id === topic.id)) {
      dispatch(setForumQuestionsFilters({ 
        topics: [...existingTopics, topic] 
      }));
    }
  };

  const removeTopic = (topicId: string) => {
    const updatedTopics = filters.topics.filter(topic => topic.id !== topicId);
    dispatch(setForumQuestionsFilters({ topics: updatedTopics }));
  };

  const setEquipmentCategory = (equipment: SearchResultI | null) => {
    dispatch(setForumQuestionsFilters({ equipmentCategory: equipment }));
  };

  const setEquipmentManufacturer = (manufacturer: SearchResultI | null) => {
    dispatch(setForumQuestionsFilters({ equipmentManufacturer: manufacturer }));
  };

  const setEquipmentModel = (model: SearchResultI | null) => {
    dispatch(setForumQuestionsFilters({ equipmentModel: model }));
  };

  const applyFilters = async () => {
    if (!isConnected) {
      showToast({ message: 'No internet connection', type: 'error' });
      return;
    }

    // Update department filter if selected
    if (departmentSelection) {
      dispatch(setForumQuestionsFilters({ department: departmentSelection }));
    }

    // Add topic if selected and not already added
    if (qnaTopicsSelection && filters.type !== 'TROUBLESHOOT') {
      addTopic(qnaTopicsSelection);
    }

    // Update equipment filters for troubleshooting
    if (filters.type === 'TROUBLESHOOT') {
      if (equipmentSelection) {
        setEquipmentCategory(equipmentSelection);
      }
      if (makeSelection) {
        setEquipmentManufacturer(makeSelection);
      }
      if (modelSelection) {
        setEquipmentModel(modelSelection);
      }
    }

    // Clear entity search selections
    dispatch(clearSelection('department'));
    dispatch(clearSelection('qnaTopics'));
    dispatch(clearSelection('equipment'));
    dispatch(clearSelection('make'));
    dispatch(clearSelection('model'));

    // Clear current questions and fetch with new filters
    dispatch(clearForumQuestions());

    try {
      await dispatch(fetchForumQuestions({ refresh: true })).unwrap();
      showToast({ message: 'Filters applied successfully', type: 'success' });
      navigation.goBack();
    } catch (error) {
      showToast({ message: 'Failed to apply filters', type: 'error' });
    }
  };

  const clearAllFilters = () => {
    dispatch(setForumQuestionsFilters({
      type: 'ALL',
      isLive: false,
      myRecommended: false,
      myQuestion: false,
      myAnswered: false,
      myCommunity: false,
      department: null,
      topics: [],
      equipmentCategory: null,
      equipmentManufacturer: null,
      equipmentModel: null,
    }));
    
    // Clear entity search selections
    dispatch(clearSelection('department'));
    dispatch(clearSelection('qnaTopics'));
    dispatch(clearSelection('equipment'));
    dispatch(clearSelection('make'));
    dispatch(clearSelection('model'));
  };

  return {
    filters,
    departmentSelection,
    qnaTopicsSelection,
    equipmentSelection,
    makeSelection,
    modelSelection,
    updateFilter,
    addTopic,
    removeTopic,
    setEquipmentCategory,
    setEquipmentManufacturer,
    setEquipmentModel,
    applyFilters,
    clearAllFilters,
  };
};

export default useForumFilter;
