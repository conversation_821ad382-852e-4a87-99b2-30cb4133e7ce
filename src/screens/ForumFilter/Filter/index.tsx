/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useState, useEffect } from 'react';
import { View, Text, Pressable } from 'react-native';
import Checkbox from '@/src/components/Checkbox';
import EntitySearch from '@/src/components/EntitySearch';
import RadioButton from '@/src/components/RadioButton';
import ToggleSwitch from '@/src/components/Toggle';
import Close from '@/src/assets/svgs/Close';
import useForumFilter from './useHook';

const Filter = () => {
  const {
    filters,
    departmentSelection,
    qnaTopicsSelection,
    equipmentSelection,
    makeSelection,
    modelSelection,
    updateFilter,
    addTopic,
    removeTopic,
    setEquipmentCategory,
    setEquipmentManufacturer,
    setEquipmentModel,
  } = useForumFilter();

  // Local state for UI
  const [qnaEnabled, setQnaEnabled] = useState(filters.type === 'NORMAL');
  const [troubleshootingEnabled, setTroubleshootingEnabled] = useState(filters.type === 'TROUBLESHOOT');

  // Update local state when filters change
  useEffect(() => {
    setQnaEnabled(filters.type === 'NORMAL');
    setTroubleshootingEnabled(filters.type === 'TROUBLESHOOT');
  }, [filters.type]);

  // Add topic when selected from entity search
  useEffect(() => {
    if (qnaTopicsSelection && qnaEnabled) {
      addTopic(qnaTopicsSelection);
    }
  }, [qnaTopicsSelection, qnaEnabled]);

  const handleQuestionTypeChange = (type: 'NORMAL' | 'TROUBLESHOOT') => {
    updateFilter('type', type);
    if (type === 'NORMAL') {
      setQnaEnabled(true);
      setTroubleshootingEnabled(false);
    } else {
      setQnaEnabled(false);
      setTroubleshootingEnabled(true);
    }
  };

  return (
    <View className="flex-1 p-4">
      <View className="py-4">
        <ToggleSwitch
          enabled={filters.isLive}
          onToggle={() => updateFilter('isLive', !filters.isLive)}
          label="Live questions only"
        />
      </View>
      <View className="py-2">
        <ToggleSwitch
          enabled={filters.myRecommended}
          onToggle={() => updateFilter('myRecommended', !filters.myRecommended)}
          label="Recommended questions only"
        />
      </View>
      <Text className="text-base font-medium text-black py-4">Question type</Text>
      <View className="flex-col gap-4">
        <Checkbox
          size={20}
          label="Questions I asked"
          checked={filters.myQuestion}
          onValueChange={(value) => updateFilter('myQuestion', value)}
        />
        <Checkbox
          size={20}
          label="Questions I answered"
          checked={filters.myAnswered}
          onValueChange={(value) => updateFilter('myAnswered', value)}
        />
        <Checkbox
          size={20}
          label="Questions from my communities"
          checked={filters.myCommunity}
          onValueChange={(value) => updateFilter('myCommunity', value)}
        />
        <Checkbox
          size={20}
          label="Questions from all communities"
          checked={!filters.myCommunity}
          onValueChange={() => updateFilter('myCommunity', false)}
        />
      </View>
      {/* <Text className="text-base font-medium text-black py-4">Posted when</Text> */}
      {/* <View className="flex-row gap-4">
        <View className="flex-1">
          <DatePicker title="From" selectedDate="" onDateChange={() => {}} />
        </View>
        <View className="flex-1">
          <DatePicker title="To" selectedDate="" onDateChange={() => {}} />
        </View>
      </View> */}
      {/* <Text className="text-base font-medium text-black py-4">Connections</Text> */}
      {/* <View className="flex-row gap-8">
        <View>
          <Checkbox size={20} label="1st" />
        </View>
        <View>
          <Checkbox size={20} label="2nd" />
        </View>
        <View>
          <Checkbox size={20} label="3rd" />
        </View>
      </View> */}
      <EntitySearch
        titleClassName="text-base text-black py-2"
        title="Department"
        selectionKey="filterDepartment"
        placeholder="Select department"
        data={departmentSelection ? departmentSelection.name : ''}
      />
      <Text className="text-base font-medium text-black py-4">Question type</Text>
      <View className="flex-col gap-2">
        <RadioButton
          selected={qnaEnabled}
          onPress={() => handleQuestionTypeChange('NORMAL')}
          label="QnA"
          inFilter={true}
        />
        {qnaEnabled && (
          <View>
            <EntitySearch
              titleClassName="text-base text-black py-2"
              title="Topics"
              selectionKey="filterTopic"
              placeholder="Select topic/s"
              multipleSelection={true}
              data={qnaTopicsSelection ? qnaTopicsSelection.name : ''}
            />
            {filters.topics.length > 0 && (
              <View className="flex-row gap-2 flex-wrap">
                {filters.topics.map((topic) => (
                  <View key={topic.id} className="flex-row items-center gap-1 bg-[#F9F9F9] rounded-lg mr-2 px-2 py-1">
                    <Text className="text-sm text-black">
                      {topic.name}
                    </Text>
                    <Pressable onPress={() => removeTopic(topic.id)}>
                      <Close width={1.8} height={1.8} />
                    </Pressable>
                  </View>
                ))}
              </View>
            )}
          </View>
        )}
        <RadioButton
          selected={troubleshootingEnabled}
          onPress={() => handleQuestionTypeChange('TROUBLESHOOT')}
          label="Troubleshooting"
          inFilter={true}
        />
        {troubleshootingEnabled && (
          <View className="flex-col gap-2">
            <EntitySearch
              titleClassName="text-base text-black py-2"
              title="Equipment"
              selectionKey="filterEquipmentCategory"
              placeholder="Select equipment/s"
              data={equipmentSelection ? equipmentSelection.name : ''}
            />
            {filters.equipmentCategory && (
              <View className="flex-row gap-2 flex-wrap">
                <View className="flex-row items-center gap-1 bg-[#F9F9F9] rounded-lg mr-2 px-2 py-1">
                  <Text className="text-sm text-black">
                    {filters.equipmentCategory.name}
                  </Text>
                  <Pressable onPress={() => setEquipmentCategory(null)}>
                    <Close width={1.8} height={1.8} />
                  </Pressable>
                </View>
              </View>
            )}
            <EntitySearch
              titleClassName="text-base text-black py-2"
              title="Make"
              selectionKey="filterEquipmentManufacturer"
              placeholder="Select make/s"
              data={makeSelection ? makeSelection.name : ''}
            />
            {filters.equipmentManufacturer && (
              <View className="flex-row gap-2 flex-wrap">
                <View className="flex-row items-center gap-1 bg-[#F9F9F9] rounded-lg mr-2 px-2 py-1">
                  <Text className="text-sm text-black">
                    {filters.equipmentManufacturer.name}
                  </Text>
                  <Pressable onPress={() => setEquipmentManufacturer(null)}>
                    <Close width={1.8} height={1.8} />
                  </Pressable>
                </View>
              </View>
            )}
            <EntitySearch
              titleClassName="text-base text-black py-2"
              title="Model"
              selectionKey="filterEquipmentModel"
              placeholder="Select model/s"
              data={modelSelection ? modelSelection.name : ''}
            />
            {filters.equipmentModel && (
              <View className="flex-row gap-2 flex-wrap">
                <View className="flex-row items-center gap-1 bg-[#F9F9F9] rounded-lg mr-2 px-2 py-1">
                  <Text className="text-sm text-black">
                    {filters.equipmentModel.name}
                  </Text>
                  <Pressable onPress={() => setEquipmentModel(null)}>
                    <Close width={1.8} height={1.8} />
                  </Pressable>
                </View>
              </View>
            )}
          </View>
        )}
      </View>
    </View>
  );
};

export default Filter;
