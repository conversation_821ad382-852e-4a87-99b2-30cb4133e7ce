import React, { SetStateAction, useState } from 'react';
import { TouchableOpacity, View } from 'react-native';
import { useSelector } from 'react-redux';
import { SectionHeader } from '@/src/components/SectionHeader';
import Tabs from '@/src/components/Tabs';
import { selectAboutProfileMaritimeSkillsCount } from '@/src/redux/selectors/about';
import { navigate } from '@/src/utilities/navigation';
import EditPencil from '@/src/assets/svgs/EditPencil';
import Skill from '@/src/assets/svgs/Skills';
import MaritimeSkills from './components/MaritimeSkills';
import OtherSkills from './components/OtherSkills';
import { ProfileSkillsPropsI, SkillsTabsI } from './types';

const ProfileSkill = ({ isUserProfile, profileId }: ProfileSkillsPropsI) => {
  const maritimeSkillsCount = useSelector(selectAboutProfileMaritimeSkillsCount);

  const tabs = [
    {
      id: 'maritimeSkills',
      label: 'Maritime Skills',
    },
    {
      id: 'otherSkills',
      label: 'Other Skills',
    },
  ];

  const tabScreens: SkillsTabsI = {
    maritimeSkills: <MaritimeSkills />,
    otherSkills: <OtherSkills />,
  };

  const [activeTab, setActiveTab] = useState<keyof SkillsTabsI>('maritimeSkills');

  return (
    <View className="">
      <View className="flex-row items-center justify-between">
        <SectionHeader title="Skills" icon={Skill} />
        {isUserProfile && (
          <TouchableOpacity
            onPress={() =>
              navigate('EditSkillsList', {
                category: activeTab,
              })
            }
          >
            <EditPencil width={2.3} height={2.3} />
          </TouchableOpacity>
        )}
      </View>
      <>
        <Tabs
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={setActiveTab as React.Dispatch<SetStateAction<string>>}
        />
        {tabScreens[activeTab]}
      </>
    </View>
  );
};

export default ProfileSkill;
