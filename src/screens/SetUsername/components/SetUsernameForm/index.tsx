import { ActivityIndicator, View } from 'react-native';
import { type RouteProp, useRoute } from '@react-navigation/native';
import { Controller } from 'react-hook-form';
import Button from '@/src/components/Button';
import TextInput from '@/src/components/TextInput';
import TextView from '@/src/components/TextView';
import type { AuthStackParamListI } from '@/src/navigation/types';
import useSetUsername from './useHook';

const SetUsernameForm = () => {
  const route = useRoute<RouteProp<AuthStackParamListI, 'SetUsername'>>();
  const email = route.params?.email || '';
  const { methods, isSubmitting, onSubmit, canSubmit, errorMessage, isCheckingUsername } =
    useSetUsername(email);

  const { control, handleSubmit } = methods;

  return (
    <View className="flex-1 bg-white">
      <View className="px-5">
        <View className="my-8">
          <TextView title="Set username" subtitle="Choose a unique username for your account" />
        </View>
        <View>
          <Controller
            control={control}
            name="userName"
            rules={{
              required: 'Username is required',
              minLength: {
                value: 4,
                message: 'Username must be at least 4 characters',
              },
              pattern: {
                value: /^(?![_.])[a-zA-Z0-9._]{4,25}(?<![_.])$/,
                message:
                  'Username must be 4-25 characters, alphanumeric with dots/underscores allowed',
              },
            }}
            render={({ field: { onChange, value, onBlur } }) => (
              <View>
                <TextInput
                  label="Username"
                  placeholder="Enter username (min 4 characters)"
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  error={errorMessage}
                  editable={!isSubmitting}
                />
                {isCheckingUsername && (
                  <View className="mt-2 flex-row items-center gap-2">
                    <ActivityIndicator size="small" color={'#448600'} />
                    <TextView
                      titleClassName="text-sm text-gray-600"
                      title="Checking availability..."
                    />
                  </View>
                )}
              </View>
            )}
          />
        </View>
        <View className="mt-6">
          <Controller
            control={control}
            name="email"
            render={({ field: { value } }) => (
              <TextInput label="Email ID" value={value} editable={false} />
            )}
          />
        </View>
        <View className="mt-8">
          <Button
            onPress={handleSubmit(onSubmit)}
            disabled={!canSubmit || isSubmitting}
            label="Save username"
            variant={canSubmit ? 'primary' : 'tertiary'}
            loading={isSubmitting}
            labelClassName="text-base font-medium"
          />
        </View>
      </View>
    </View>
  );
};

export default SetUsernameForm;
