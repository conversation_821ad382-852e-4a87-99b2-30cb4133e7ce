/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React, { useState } from 'react';
import { View, Text, Pressable, Image } from 'react-native';
import Clipboard from '@react-native-clipboard/clipboard';
import { useSelector } from 'react-redux';
import BottomSheet from '@/src/components/Bottomsheet';
import CustomModal from '@/src/components/Modal';
import { OptionItem, OptionsMenu } from '@/src/components/OptionsMenu';
import UserAvatar from '@/src/components/UserAvatar';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { showToast } from '@/src/utilities/toast';
import { useAnswerVoting } from '@/src/hooks/useVoting';
import AiBot from '@/src/assets/images/others/aibot.png';
import Comment from '@/src/assets/svgs/Comment';
import Copy from '@/src/assets/svgs/Copy';
import DownVote from '@/src/assets/svgs/DownVote';
import EditPencil from '@/src/assets/svgs/EditPencil';
// Testing with a bot avatar
import HorizontalEllipsis from '@/src/assets/svgs/HorizontalEllipsis';
import ReportFlag from '@/src/assets/svgs/ReportFlag';
import Share from '@/src/assets/svgs/Share';
import SolvedIcon from '@/src/assets/svgs/SolvedIcon';
import TrashBin from '@/src/assets/svgs/TrashBin';
import UpVote from '@/src/assets/svgs/UpVote';
import { ForumAnswerProps } from './types';

const Answers: React.FC<{ answer: ForumAnswerProps }> = ({ answer }) => {
  const {
    answerId,
    userId: profileId,
    content,
    upVotes,
    downVotes,
    comments,
    answerVerified = false, // Default to false if not provided
  } = answer;

  const currentUser = useSelector(selectCurrentUser);

  // Voting functionality
  const {
    handleUpvote,
    handleDownvote,
    isUpvoted,
    isDownvoted,
    isLoading: voteLoading
  } = useAnswerVoting(answerId);

  const [optionsVisible, setOptionsVisible] = useState(false);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [pendingDeleteAction, setPendingDeleteAction] = useState(false);

  const isOwnPost = currentUser?.profileId === profileId;

  const handleOptions = () => setOptionsVisible(true);

  const handleCloseOptions = () => setOptionsVisible(false);

  const handleBottomSheetHide = () => {
    if (pendingDeleteAction) {
      setDeleteModalVisible(true);
      setPendingDeleteAction(false);
    }
  };

  const showDeleteConfirmation = () => {
    setPendingDeleteAction(true);
    setOptionsVisible(false);
  };

  const confirmDelete = async () => {
    setIsDeleting(true);
    setIsDeleting(false);
    setDeleteModalVisible(false);
    showToast({
      type: 'success',
      message: 'Deleted',
      description: 'Post deleted successfully',
    });
  };

  const cancelDelete = () => setDeleteModalVisible(false);

  const handleEdit = () => {
    handleCloseOptions();
  };

  const handleReport = () => {
    showToast({
      type: 'success',
      message: 'Reported',
      description: 'Post reported',
    });
    handleCloseOptions();
  };

  const handleCopyLink = () => {
    const postUrl = `https://network.navicater.com/forum/post/${answerId}`;
    Clipboard.setString(postUrl);
    showToast({
      type: 'success',
      message: 'Link Copied',
      description: 'Post link copied to clipboard',
    });
    handleCloseOptions();
  };

  return (
    <View className="bg-white overflow-hidden py-2 border-b border-gray-200 mb-3 rounded-lg">
      <View className="px-4 flex-row items-center justify-between">
        <View className="flex-row gap-2 items-center">
          <Pressable>
            <UserAvatar avatarUri={Image.resolveAssetSource(AiBot).uri} width={28} height={28} />
            {/* Replace with actual user avatar */}
          </Pressable>
          <Text className="text-base font-normal text-[#262626]">
            {/* Replace with actual user name */}
            User Name
          </Text>
        </View>
        <View className="flex-row gap-2 items-center">
          {answerVerified && <SolvedIcon width={2} height={2} />}
          {!answerVerified && (
            <Pressable>
              <Text className="text-base items-center font-medium text-[#448600]">Verify</Text>
            </Pressable>
          )}
          <Pressable onPress={handleOptions}>
            <HorizontalEllipsis width={2.5} height={2.5} />
          </Pressable>
        </View>
      </View>
      <View className="px-4 py-2">
        <Text className="pl-10 text-base font-normal text-[#262626]">{content}</Text>
      </View>
      <View className="flex-row justify-between items-center px-4 py-2">
        <View className="flex-row items-center gap-5 pl-10">
          <View className="flex-row items-center gap-2">
            <Pressable onPress={handleUpvote} disabled={voteLoading}>
              <UpVote width={2.5} height={2.5} isLiked={isUpvoted} />
            </Pressable>
            <Text className="text-[#262626] text-sm font-medium">{upVotes}</Text>
          </View>
          <View className="flex-row items-center gap-2">
            <Pressable onPress={handleDownvote} disabled={voteLoading}>
              <DownVote width={2.5} height={2.5} isLiked={isDownvoted} />
            </Pressable>
            <Text className="text-[#262626] text-sm font-medium">{downVotes}</Text>
          </View>
          <View>
            <Pressable className="flex-row items-center gap-2">
              <Comment width={2.5} height={2.5} color="#525252" />
              <Text className="text-[#262626] text-sm font-medium">{comments}</Text>
            </Pressable>
          </View>
        </View>
        <Pressable>
          <Share width={2.5} height={2.5} />
        </Pressable>
      </View>
      <BottomSheet
        height={isOwnPost ? 230 : 200}
        visible={optionsVisible}
        onClose={handleCloseOptions}
        onModalHide={handleBottomSheetHide}
      >
        <OptionsMenu>
          {isOwnPost ? (
            <>
              <OptionItem
                icon={<TrashBin stroke="#EF4444" strokeWidth={1.5} width={2} height={2} />}
                label="Delete answer"
                textClassName="text-red-500"
                onPress={showDeleteConfirmation}
              />
            </>
          ) : (
            <OptionItem
              icon={<ReportFlag stroke="#374151" strokeWidth={1.5} width={2} height={2} />}
              label="Report answer"
              onPress={handleReport}
            />
          )}
          <View className="h-[1px] bg-gray-200 my-2" />
          <OptionItem
            icon={<Copy stroke="#374151" strokeWidth={1.5} width={2} height={2} />}
            label="Copy Link"
            onPress={handleCopyLink}
          />
          <View className="h-[1px] bg-gray-200 my-2" />
          {isOwnPost && (
            <OptionItem
              icon={<EditPencil stroke="#374151" strokeWidth={1.5} width={2} height={2} />}
              label="Edit answer"
              onPress={handleEdit}
            />
          )}
        </OptionsMenu>
      </BottomSheet>
      <CustomModal
        isVisible={deleteModalVisible}
        title="Delete Answer"
        description="Are you sure you want to delete this answer? This action cannot be undone."
        confirmText="Delete"
        confirmButtonVariant="danger"
        cancelText="Cancel"
        onConfirm={confirmDelete}
        onCancel={cancelDelete}
        isConfirming={isDeleting}
      />
    </View>
  );
};

export default Answers;
