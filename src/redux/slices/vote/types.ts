/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/

import type { VoteTypeE } from '@/src/networks/forum/types';

export type VoteStateI = {
  // Track user's votes for questions
  questionVotes: Record<string, VoteTypeE | null>; // questionId -> vote type or null
  // Track user's votes for answers
  answerVotes: Record<string, VoteTypeE | null>; // answerId -> vote type or null
  // Loading states
  questionVoteLoading: Record<string, boolean>; // questionId -> loading state
  answerVoteLoading: Record<string, boolean>; // answerId -> loading state
  // Error states
  error: string | null;
};

export type VoteQuestionPayloadI = {
  questionId: string;
  type: VoteTypeE;
};

export type VoteAnswerPayloadI = {
  answerId: string;
  type: VoteTypeE;
};

export type RemoveQuestionVotePayloadI = {
  questionId: string;
};

export type RemoveAnswerVotePayloadI = {
  answerId: string;
};
