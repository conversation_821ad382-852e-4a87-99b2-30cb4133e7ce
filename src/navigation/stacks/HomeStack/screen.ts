import type { HomeStackParamListI, StackScreenI } from '@/src/navigation/types';
import AIChatScreen from '@/src/screens/AIChat';
import ChatScreen from '@/src/screens/Chat';
import ChatsScreen from '@/src/screens/Chats';
import CommentScreen from '@/src/screens/Comment';
import ConnectionScreen from '@/src/screens/Connection';
import EditCertificationListScreen from '@/src/screens/EditCertificationList';
import EditDocumentsListScreen from '@/src/screens/EditDocumentsList';
import EditEducationListScreen from '@/src/screens/EditEducationList';
import GlobalSearchScreen from '@/src/screens/GlobalSearch';
import HomeScreen from '@/src/screens/Home';
import LikesScreen from '@/src/screens/Likes';
import NotFoundScreen from '@/src/screens/NotFound';
import PortProfileScreen from '@/src/screens/PortProfile';
import ShipProfileScreen from '@/src/screens/ShipProfile';
import OtherUserProfileScreen from '@/src/screens/UserProfile';
import { withErrorBoundary } from '@/src/hocs/withErrorBoundary';

const HomeScreenWithErrorBoundary = withErrorBoundary(HomeScreen, {
  title: 'Home Feed Error',
  subtitle: 'Something went wrong loading the home feed. Please try again.',
});

const CommentScreenWithErrorBoundary = withErrorBoundary(CommentScreen, {
  title: 'Comments Error',
  subtitle: 'Something went wrong loading comments. Please try again.',
});

const LikesScreenWithErrorBoundary = withErrorBoundary(LikesScreen, {
  title: 'Likes Error',
  subtitle: 'Something went wrong loading likes. Please try again.',
});

const ShipProfileScreenWithErrorBoundary = withErrorBoundary(ShipProfileScreen, {
  title: 'Ship Profile Error',
  subtitle: 'Something went wrong loading the ship profile. Please try again.',
});

const PortProfileScreenWithErrorBoundary = withErrorBoundary(PortProfileScreen, {
  title: 'Port Profile Error',
  subtitle: 'Something went wrong loading the port profile. Please try again.',
});

const OtherUserProfileScreenWithErrorBoundary = withErrorBoundary(OtherUserProfileScreen, {
  title: 'User Profile Error',
  subtitle: 'Something went wrong loading the user profile. Please try again.',
});

const ConnectionScreenWithErrorBoundary = withErrorBoundary(ConnectionScreen, {
  title: 'Connections Error',
  subtitle: 'Something went wrong loading connections. Please try again.',
});

const ChatsScreenWithErrorBoundary = withErrorBoundary(ChatsScreen, {
  title: 'Chats Error',
  subtitle: 'Something went wrong loading your chats. Please try again.',
});

const ChatScreenWithErrorBoundary = withErrorBoundary(ChatScreen, {
  title: 'Chat Error',
  subtitle: 'Something went wrong in the chat. Please try again.',
});

const GlobalSearchScreenWithErrorBoundary = withErrorBoundary(GlobalSearchScreen, {
  title: 'Search Error',
  subtitle: 'Something went wrong during search. Please try again.',
});

const NotFoundScreenWithErrorBoundary = withErrorBoundary(NotFoundScreen, {
  title: 'Page Not Found Error',
  subtitle: 'Something went wrong loading this page. Please try again.',
});

const AIChatScreenWithErrorBoundary = withErrorBoundary(AIChatScreen, {
  title: 'AI Chat Error',
  subtitle: 'Something went wrong with the AI chat. Please try again.',
});

const EditEducationListScreenWithErrorBoundary = withErrorBoundary(EditEducationListScreen, {
  title: 'Education Error',
  subtitle: 'Something went wrong loading your education. Please try again.',
});

const EditCertificationListScreenWithErrorBoundary = withErrorBoundary(
  EditCertificationListScreen,
  {
    title: 'Certifications Error',
    subtitle: 'Something went wrong loading your certifications. Please try again.',
  },
);

const EditDocumentsListScreenWithErrorBoundary = withErrorBoundary(EditDocumentsListScreen, {
  title: 'Documents Error',
  subtitle: 'Something went wrong loading your documents. Please try again.',
});

export const screens: StackScreenI<HomeStackParamListI>[] = [
  { name: 'Home', component: HomeScreenWithErrorBoundary },
  { name: 'Comment', component: CommentScreenWithErrorBoundary },
  { name: 'Likes', component: LikesScreenWithErrorBoundary },
  { name: 'ShipProfile', component: ShipProfileScreenWithErrorBoundary },
  { name: 'PortProfile', component: PortProfileScreenWithErrorBoundary },
  { name: 'OtherUserProfile', component: OtherUserProfileScreenWithErrorBoundary },
  { name: 'Connection', component: ConnectionScreenWithErrorBoundary },
  { name: 'Chats', component: ChatsScreenWithErrorBoundary },
  { name: 'Chat', component: ChatScreenWithErrorBoundary },
  { name: 'GlobalSearch', component: GlobalSearchScreenWithErrorBoundary },
  { name: 'NotFound', component: NotFoundScreenWithErrorBoundary },
  { name: 'AIChat', component: AIChatScreenWithErrorBoundary },
  { name: 'EditEducationList', component: EditEducationListScreenWithErrorBoundary },
  { name: 'EditCertificationList', component: EditCertificationListScreenWithErrorBoundary },
  { name: 'EditDocumentList', component: EditDocumentsListScreenWithErrorBoundary },
];
