export type QuestionTypeE = 'NORMAL' | 'TROUBLESHOOT';

export type IdTypeI = {
  id: string;
  dataType: 'master' | 'raw';
};

export type QuestionMediaCreateItemI = {
  fileUrl: string;
  fileExtension: string;
};

export interface CreateQuestionPayloadI {
  title: string;
  description: string;
  type: QuestionTypeE;
  communityId: string;
  equipmentCategory?: IdTypeI;
  equipmentModel?: IdTypeI;
  equipmentManufacturer?: IdTypeI;
  topics?: IdTypeI[];
  department?: IdTypeI;
  isAnonymous?: boolean;
  files?: QuestionMediaCreateItemI[];
}

export interface QuestionCreateResponseI {
  id: string;
  title: string;
  description: string;
  type: QuestionTypeE;
}

export interface QuestionI {
  id: string;
  title: string;
  description: string;
  type: QuestionTypeE;
  communityId: string;
  isAnonymous?: boolean;
  createdAt: string;
  updatedAt: string;
  authorId: string;
}

export type ForumQuestionFetchManyPayloadI = {
  cursorDate?: string | null;
  pageSize?: string;
  type?: 'ALL' | 'NORMAL' | 'TROUBLESHOOT';
  isLive?: boolean;
  myRecommended?: boolean;
  myQuestion?: boolean;
  myAnswered?: boolean;
  myCommunity?: boolean;
  department?: IdTypeI;
  topics?: IdTypeI[];
  equipmentCategory?: IdTypeI;
  equipmentManufacturer?: IdTypeI;
  equipmentModel?: IdTypeI;
};

export type TopicI = {
  topicId?: string;
  topicName?: string;
  topicRawDataId?: string;
  topicRawDataName?: string;
};

export type ForumQuestionI = {
  id: string;
  title: string;
  description: string;
  type: QuestionTypeE;
  upvoteCount: number;
  downvoteCount: number;
  answerCount: number;
  isSolved: boolean;
  isEdited: boolean;
  isAnonymous: boolean;
  liveStartedAt: string;
  equipmentCategory?: IdTypeI & { name: string } | null;
  equipmentModel?: IdTypeI & { name: string } | null;
  equipmentManufacturer?: IdTypeI & { name: string } | null;
  topics?: TopicI[] | null;
};

export type ForumQuestionFetchManyResultI = {
  data: ForumQuestionI[];
  nextCursorDate: string | null;
  total: number;
};
