/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/

import { apiCall } from '@/src/services/api';
import type {
  ForumQuestionVoteI,
  ForumQuestionVoteFetchManyI,
  ForumQuestionVoteFetchManyResultI,
  ForumQuestionVoteCreateOneI,
  ForumQuestionVoteDeleteOneI,
  ForumAnswerVoteI,
  ForumAnswerVoteFetchManyI,
  ForumAnswerVoteFetchManyResultI,
  ForumAnswerVoteCreateOneI,
  ForumAnswerVoteDeleteOneI,
} from './types';

// Question Vote APIs
export const fetchQuestionVotesAPI = async (
  query: ForumQuestionVoteFetchManyI,
): Promise<ForumQuestionVoteFetchManyResultI> => {
  const result = await apiCall<unknown, ForumQuestionVoteFetchManyResultI>(
    '/backend/api/v1/forum/question-votes',
    'GET',
    {
      isAuth: true,
      query,
    },
  );
  return result;
};

export const createQuestionVoteAPI = async (
  payload: ForumQuestionVoteCreateOneI,
): Promise<ForumQuestionVoteI> => {
  const result = await apiCall<ForumQuestionVoteCreateOneI, ForumQuestionVoteI>(
    '/backend/api/v1/forum/question-vote',
    'POST',
    {
      isAuth: true,
      payload,
    },
  );
  return result;
};

export const deleteQuestionVoteAPI = async (
  query: ForumQuestionVoteDeleteOneI,
): Promise<void> => {
  await apiCall<unknown, void>(
    '/backend/api/v1/forum/question-vote',
    'DELETE',
    {
      isAuth: true,
      query,
    },
  );
};

// Answer Vote APIs (similar structure to question votes)
export const fetchAnswerVotesAPI = async (
  query: ForumAnswerVoteFetchManyI,
): Promise<ForumAnswerVoteFetchManyResultI> => {
  const result = await apiCall<unknown, ForumAnswerVoteFetchManyResultI>(
    '/backend/api/v1/forum/answer-votes',
    'GET',
    {
      isAuth: true,
      query,
    },
  );
  return result;
};

export const createAnswerVoteAPI = async (
  payload: ForumAnswerVoteCreateOneI,
): Promise<ForumAnswerVoteI> => {
  const result = await apiCall<ForumAnswerVoteCreateOneI, ForumAnswerVoteI>(
    '/backend/api/v1/forum/answer-vote',
    'POST',
    {
      isAuth: true,
      payload,
    },
  );
  return result;
};

export const deleteAnswerVoteAPI = async (
  query: ForumAnswerVoteDeleteOneI,
): Promise<void> => {
  await apiCall<unknown, void>(
    '/backend/api/v1/forum/answer-vote',
    'DELETE',
    {
      isAuth: true,
      query,
    },
  );
};
