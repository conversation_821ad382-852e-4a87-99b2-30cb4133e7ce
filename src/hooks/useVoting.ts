/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/

import { useDispatch, useSelector } from 'react-redux';
import type { AppDispatch } from '@/src/redux/store';
import {
  voteQuestion,
  voteAnswer,
  removeQuestionVote,
  removeAnswerVote,
} from '@/src/redux/slices/vote/voteSlice';
import {
  selectQuestionVoteByQuestionId,
  selectAnswerVoteByAnswerId,
  selectQuestionVoteLoadingByQuestionId,
  selectAnswerVoteLoadingByAnswerId,
} from '@/src/redux/selectors/vote';
import type { VoteTypeE } from '@/src/networks/forum/types';

export const useQuestionVotingI = (questionId: string) => {
  const dispatch = useDispatch<AppDispatch>();
  const currentVote = useSelector(selectQuestionVoteByQuestionId(questionId));
  const isLoading = useSelector(selectQuestionVoteLoadingByQuestionId(questionId));

  const handleUpvote = () => {
    if (currentVote === 'UPVOTE') {
      dispatch(removeQuestionVote({ questionId }));
    } else {
      dispatch(voteQuestion({ questionId, type: 'UPVOTE' }));
    }
  };

  const handleDownvote = () => {
    if (currentVote === 'DOWNVOTE') {
      dispatch(removeQuestionVote({ questionId }));
    } else {
      dispatch(voteQuestion({ questionId, type: 'DOWNVOTE' }));
    }
  };

  const removeVote = () => {
    dispatch(removeQuestionVote({ questionId }));
  };

  return {
    isLoading,
    handleUpvote,
    handleDownvote,
    removeVote,
    isUpvoted: () => currentVote === 'UPVOTE',
    isDownvoted: () => currentVote === 'DOWNVOTE',
  };
};

export const useAnswerVoting = (answerId: string) => {
  const dispatch = useDispatch<AppDispatch>();
  
  const currentVote = useSelector(selectAnswerVoteByAnswerId(answerId));
  const isLoading = useSelector(selectAnswerVoteLoadingByAnswerId(answerId));

  const handleUpvote = () => {
    dispatch(voteAnswer({ answerId, type: 'UPVOTE' }));
  };

  const handleDownvote = () => {
    dispatch(voteAnswer({ answerId, type: 'DOWNVOTE' }));
  };

  const removeVote = () => {
    dispatch(removeAnswerVote({ answerId }));
  };

  return {
    currentVote,
    isLoading,
    handleUpvote,
    handleDownvote,
    removeVote,
    isUpvoted: currentVote === 'UPVOTE',
    isDownvoted: currentVote === 'DOWNVOTE',
  };
};
